<template>
  <FormItem name="product.category_id" label="商品类目" class="item-form-category-select">
    <Cascader :class="{'product-category': categoryText}" :model-value="modelValue" :loading="categoryLoading" :options="categoryOptions"
              leaf-only :load-data="categoryLoadData" :placeholder="categoryText ? categoryText : '请选择商品类目'" :disabled="componentProps?.disabled" @change="onCategoryChange"
    >
      <template v-if="isLifeHotel" #bottom>
        <Text class="item-form-category-select-cascader-bottom">未找到？<Text class="action" bold @click="openModal">新增类目</Text></Text>
      </template>
    </Cascader>
  </FormItem>
  <CategoryAndQualificationModal v-model:visible="visible" from="item" class="item-category-qualification-modal" />
</template>

<script setup lang="ts">
  import { ref, onBeforeMount, watch } from 'vue'
  import {
    Cascader, FormItem2 as FormItem, Text
  } from '@xhs/delight'
  import { tryCatch } from 'shared/utils'
  import CategoryAndQualificationModal from 'shared/components/CategoryAndQualificationModal/index.vue'
  import useUser from 'shared/seller/composables/use-user'
  import { RI_LI_FANG_CATEGORY_IDS } from '~/constant'
  import { get_children_categories, get_marketable_categories } from './services'

  const props = defineProps<{
    modelValue: string
    componentProps?: {
      disabled?: boolean
    }
  }>()

  const { isLifeHotel } = useUser()

  const visible = ref(false)
  const openModal = () => {
    window.document.body.click()
    visible.value = true
  }
  const emit = defineEmits(['update:modelValue'])

  // 处理组件内部逻辑
  const categoryLoading = ref(false)
  const categoryOptions = ref<any[]>([])
  const categoryText = ref('')

  const categoryLoadData = async (option: { id: any }, resolve) => {
    try {
      const res = await get_children_categories({ categoryId: option.id })
      const childrenInfos = res?.childrenInfos?.map(item => {
        if (String(item.is_leaf) === 'true') {
          const disabled = RI_LI_FANG_CATEGORY_IDS.includes(item.id)
          return {
            ...item,
            label: item.name,
            value: item.id,
            disabled,
            tooltip: disabled ? '暂未支持选择该商品类目' : '',
          }
        }
        return {
          ...item, label: item.name, value: item.id, children: []
        }
      })
      resolve(childrenInfos)
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(error)
    }
  }

  const fetchCategoryLevel = async () => {
    categoryLoading.value = true
    const [res, err] = await tryCatch(get_marketable_categories)
    if (!err && res) {
      categoryOptions.value = res?.itemInfos?.map(item => {
        if (String(item.is_leaf) === 'true') {
          return { ...item, label: item.name, value: item.id }
        }
        return {
          ...item, label: item.name, value: item.id, children: []
        }
      })
    }
    categoryLoading.value = false
  }

  const onCategoryChange = (val: string) => {
    emit('update:modelValue', val)
  }

  onBeforeMount(() => {
    fetchCategoryLevel()
  })

  const setCategoryText = async (categoryId: string) => {
    try {
      const { paths } = await get_children_categories({ categoryId }) || {}
      categoryText.value = paths.map(path => path?.name).join(' / ')
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(error)
    }
  }

  watch(() => props.modelValue, (val: string) => {
    if (val) {
      setCategoryText(val)
    }
  }, {
    immediate: true,
  })

</script>

<style scoped lang="stylus">
.product-category {
  /deep/ .d-cascader-placeholder {
    color: var(--color-text-title) !important
  }
}
</style>

<style lang="stylus">
.item-form-category-select-cascader-bottom
  padding 12px 16px
  border-top: 1px solid var(--line-overlay-divider, rgba(0, 0, 0, 0.08));
  .action
    color var(--Info-default, #386BFF)
    cursor pointer
</style>
