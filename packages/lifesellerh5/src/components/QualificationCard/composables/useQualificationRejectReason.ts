import { computed } from 'vue'
import { useStore } from 'vuex'

/**
 * 解析资质fieldCode，提取categoryId和qualificationId信息
 * fieldCode格式：merchantCategoryDraft.categoryQualificationMap*64240f7b7d13e90001fa7d01.qualificationList*1097420507324764160.fileAttachmentList
 * @param fieldCode 字段编码
 */
interface ParsedQualificationFieldCode {
  categoryId?: string
  qualificationId?: string
  fieldType: 'image' | 'qualification' | 'unknown'
  qualificationName?: string
  rawFieldCode?: string
  debugInfo?: {
    categoryMatch?: string[]
    qualificationListMatch?: string[]
    originalFieldCode: string
  }
}

const parseQualificationFieldCode = (fieldCode: string): ParsedQualificationFieldCode => {
  if (!fieldCode || !fieldCode.includes('merchantCategoryDraft.categoryQualificationMap')) {
    return {
      fieldType: 'unknown',
      rawFieldCode: fieldCode,
      debugInfo: { originalFieldCode: fieldCode }
    }
  }

  // 解析categoryId：merchantCategoryDraft.categoryQualificationMap*64240f7b7d13e90001fa7d01
  const categoryMatch = fieldCode.match(/categoryQualificationMap\*([^.]+)/)
  const categoryId = categoryMatch?.[1]

  // 增强的qualificationList解析，支持更复杂的格式
  // 支持格式：
  // 1. qualificationList*1097420507324764160.fileAttachmentList (图片)
  // 2. qualificationList*补充材料（非必填） (资质名称)
  // 3. qualificationList*ID1.ID2.fileAttachmentList (多层级ID)
  const qualificationListMatch = fieldCode.match(/qualificationList\*([^.]+(?:\.[^.]+)*?)(?:\.(fileAttachmentList))?$/)

  if (!qualificationListMatch) {
    return {
      categoryId,
      fieldType: 'unknown',
      rawFieldCode: fieldCode,
      debugInfo: {
        categoryMatch: categoryMatch || undefined,
        originalFieldCode: fieldCode
      }
    }
  }

  const qualificationIdOrName = qualificationListMatch[1]
  const suffix = qualificationListMatch[2]

  let qualificationId: string | undefined
  let qualificationName: string | undefined
  let fieldType: 'image' | 'qualification' | 'unknown'

  if (suffix === 'fileAttachmentList') {
    // 图片错误：*1097420507324764160.fileAttachmentList 或 *ID1.ID2.fileAttachmentList
    qualificationId = qualificationIdOrName
    fieldType = 'image'
  } else if (!suffix) {
    // 资质类型错误：*补充材料（非必填）
    // 判断是否为纯数字ID（可能是遗漏了fileAttachmentList后缀的图片错误）
    if (/^\d+$/.test(qualificationIdOrName)) {
      // 纯数字，可能是图片ID
      qualificationId = qualificationIdOrName
      fieldType = 'image'
      console.warn(`[QualificationRejectReason] 检测到可能遗漏fileAttachmentList后缀的图片ID: ${qualificationIdOrName}`)
    } else {
      // 包含非数字字符，应该是资质名称
      qualificationName = qualificationIdOrName
      fieldType = 'qualification'
    }
  } else {
    fieldType = 'unknown'
  }

  const result = {
    categoryId,
    qualificationId,
    qualificationName,
    fieldType,
    rawFieldCode: fieldCode,
    debugInfo: {
      categoryMatch: categoryMatch || undefined,
      qualificationListMatch: qualificationListMatch || undefined,
      originalFieldCode: fieldCode
    }
  }

  // 添加调试日志
  if (process.env.NODE_ENV === 'development') {
    console.log(`[QualificationRejectReason] 解析fieldCode:`, {
      input: fieldCode,
      output: result
    })
  }

  return result
}

/**
 * 资质拒绝原因相关的composable
 */
export const useQualificationRejectReason = () => {
  const store = useStore()

  // 获取store中的拒绝原因列表
  const rejectDetailList = computed(() => store.getters['rejectReason/rejectDetailList'] || [])

  // 获取资质相关的拒绝原因
  const qualificationRejectReasons = computed(() => rejectDetailList.value.filter((item: any) => item.moduleCode === 'QUALIFICATION'
    && item.fieldCode?.includes('merchantCategoryDraft.categoryQualificationMap')))

  /**
   * 根据资质代码和字段类型获取拒绝原因
   * @param qualificationCode 资质代码
   * @param fieldType 字段类型：'image' | 'qualification'
   * @param qualificationName 资质名称（当fieldType为'qualification'时使用）
   */
  const getQualificationRejectReason = (
    qualificationCode: string,
    fieldType: 'image' | 'qualification',
    qualificationName?: string
  ): string => {
    if (!qualificationCode && !qualificationName) return ''

    const matchingReasons = qualificationRejectReasons.value.filter((item: any) => {
      const parsed = parseQualificationFieldCode(item.fieldCode)

      // 匹配字段类型
      if (parsed.fieldType !== fieldType) {
        return false
      }

      if (fieldType === 'image') {
        // 图片错误：使用多种匹配策略
        // 策略1：精确匹配qualificationId
        if (parsed.qualificationId === qualificationCode) {
          return true
        }

        // 策略2：模糊匹配 - 检查qualificationCode是否包含在qualificationId中
        if (parsed.qualificationId && qualificationCode &&
            (parsed.qualificationId.includes(qualificationCode) ||
             qualificationCode.includes(parsed.qualificationId))) {
          return true
        }

        return false
      }

      if (fieldType === 'qualification') {
        // 资质类型错误：匹配qualificationName
        if (parsed.qualificationName === qualificationName) {
          return true
        }

        // 模糊匹配资质名称
        if (parsed.qualificationName && qualificationName &&
            (parsed.qualificationName.includes(qualificationName) ||
             qualificationName.includes(parsed.qualificationName))) {
          return true
        }

        return false
      }

      return false
    })

    const result = matchingReasons
      .map((item: any) => item.rejectContent)
      .filter(Boolean)
      .join('; ')

    // 添加调试日志
    if (process.env.NODE_ENV === 'development' && (result || qualificationRejectReasons.value.length > 0)) {
      console.log(`[QualificationRejectReason] 匹配结果:`, {
        qualificationCode,
        fieldType,
        qualificationName,
        totalReasons: qualificationRejectReasons.value.length,
        matchedCount: matchingReasons.length,
        result: result || '无匹配结果'
      })
    }

    return result
  }

  /**
   * 生成用于RejectReasonDisplay的fieldCode
   * @param qualificationCode 资质代码
   * @param fieldType 字段类型
   * @param qualificationName 资质名称
   */
  const generateFieldCodeForDisplay = (
    qualificationCode: string,
    fieldType: 'image' | 'qualification',
    qualificationName?: string
  ): string => {
    const basePrefix = 'merchantCategoryDraft.categoryQualificationMap'

    // 使用通配符匹配，不依赖具体的categoryId
    if (fieldType === 'image') {
      return `${basePrefix}*.qualificationList*${qualificationCode}.fileAttachmentList`
    }

    if (fieldType === 'qualification' && qualificationName) {
      return `${basePrefix}*.qualificationList*${qualificationName}`
    }

    return ''
  }

  /**
   * 检查指定资质是否有拒绝原因
   * @param qualificationCode 资质代码
   * @param fieldType 字段类型
   * @param qualificationName 资质名称
   */
  const hasQualificationRejectReason = (
    qualificationCode: string,
    fieldType: 'image' | 'qualification',
    qualificationName?: string
  ): boolean => getQualificationRejectReason(qualificationCode, fieldType, qualificationName).length > 0

  return {
    qualificationRejectReasons,
    getQualificationRejectReason,
    generateFieldCodeForDisplay,
    hasQualificationRejectReason,
    parseQualificationFieldCode
  }
}
